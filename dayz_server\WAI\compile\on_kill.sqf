local _unit = _this select 0;
local _player = _this select 1;
local _launcher = secondaryWeapon _unit;
local _mission = _unit getVariable ("mission" + dayz_serverKey);
	
if (typeName (WAI_MissionData select _mission) == "ARRAY") then {
	WAI_MissionData select _mission set [0, ((WAI_MissionData select _mission) select 0) - 1];
};

_unit setVariable ["bodyName","NPC",false]; // Corpse will be deleted by sched_corpses function according to DZE_NPC_CleanUp_Time

if (WAI_HasMoney && Z_singleCurrency) then {
	_unit setVariable ["cashMoney", (round (random WAI_MoneyMultiplier) * 50) , true];
};

if (WAI_AddSkin) then {
	local _skin = "Skin_" + (typeOf _unit);
	if (isClass (configFile >> "CfgMagazines" >> _skin)) then {
		[_unit,_skin] call BIS_fnc_invAdd;
	};
};

if (isPlayer _player) then {
	if (WAI_RewardVehGunner) then {
		_player = (effectiveCommander vehicle _player);
	};
	
	// Check if player is authorized to kill this AI (for claimed missions)
	local _isAuthorized = true;
	if (WAI_AutoClaim) then {
		local _missionIndex = _unit getVariable ("mission" + dayz_serverKey);
		if (!isNil "_missionIndex" && {typeName (WAI_MissionData select _missionIndex) == "ARRAY"}) then {
			{
				if (typeName _x == "ARRAY" && {count _x >= 4}) then {
					local _markerData = _x select 3;
					if (typeName _markerData == "ARRAY" && {count _markerData >= 8}) then {
						local _markerName = _markerData select 1;
						if (_markerName == ("WAI" + str(_missionIndex) + "autodot")) then {
							local _markerText = _markerData select 7;
							if (typeName _markerText == "ARRAY" && {count _markerText >= 2}) then {
								local _claimerName = _markerText select 1;
								if (!isNil "_claimerName" && {_claimerName != ""}) then {
									_isAuthorized = false; // Mission is claimed, default to unauthorized
									if ((name _player) == _claimerName) then {
										_isAuthorized = true;
									} else {
										{
											if (isPlayer _x && {(name _x) == _claimerName}) then {
												if (_player in (units group _x)) then {
													_isAuthorized = true;
												};
											};
										} count playableUnits;
									};
								};
							};
						};
					};
				};
			} count DZE_ServerMarkerArray;
		};
	};

	if (WAI_KillFeed && WAI_HumanityGain) then {
		local _aitype = ["Bandit","Hero"] select (_unit getVariable ["Hero", false]);
		local _humanityReward = "";
		if (_isAuthorized) then {
			_humanityReward = [format["+%1 Humanity",WAI_AddHumanity],format["-%1 Humanity",WAI_RemoveHumanity]] select (_aitype == "Hero");
		} else {
			_humanityReward = [format["-%1 Humanity (Unauthorized)",WAI_AddHumanity],format["-%1 Humanity (Unauthorized)",WAI_RemoveHumanity]] select (_aitype == "Hero");
		};

		local _aiColor = ["#ff0000","#3333ff"] select (_aitype == "Hero");
		local _params = [_aiColor,"0.50","#FFFFFF",-.4,.2,2,0.5];

		RemoteMessage = ["ai_killfeed", [_aitype," AI Kill",_humanityReward],_params];
		(owner _player) publicVariableClient "RemoteMessage";
	};

	if (WAI_HumanityGain) then {
		local _humanity = _player getVariable["humanity",0];
		local _gain = _unit getVariable ["humanity", 0];
		local _shouldGainHumanity = true;

		// Check if this mission is claimed and if the killer is authorized
		if (WAI_AutoClaim) then {
			// Find the mission data for this AI unit
			local _missionIndex = _unit getVariable ("mission" + dayz_serverKey);
			if (!isNil "_missionIndex" && {typeName (WAI_MissionData select _missionIndex) == "ARRAY"}) then {
				// Look through all server markers to find this mission's claim data
				{
					if (typeName _x == "ARRAY" && {count _x >= 4}) then {
						local _markerData = _x select 3;
						if (typeName _markerData == "ARRAY" && {count _markerData >= 8}) then {
							local _markerName = _markerData select 1;
							// Check if this is the marker for our mission
							if (_markerName == ("WAI" + str(_missionIndex) + "autodot")) then {
								local _markerText = _markerData select 7;
								if (typeName _markerText == "ARRAY" && {count _markerText >= 2}) then {
									// Extract the claimer's name from the marker text
									local _claimerName = _markerText select 1;
									if (!isNil "_claimerName" && {_claimerName != ""}) then {
										// Check if the killer is the claimer or in their group
										local _isAuthorized = false;
										if ((name _player) == _claimerName) then {
											_isAuthorized = true;
										} else {
											// Check if killer is in the same group as the claimer
											{
												if (isPlayer _x && {(name _x) == _claimerName}) then {
													if (_player in (units group _x)) then {
														_isAuthorized = true;
													};
												};
											} count playableUnits;
										};

										if (!_isAuthorized) then {
											_shouldGainHumanity = false;
										};
									};
								};
							};
						};
					};
				} count DZE_ServerMarkerArray;
			};
		};

		// Apply humanity gain or loss based on authorization
		if (_shouldGainHumanity) then {
			// Authorized player gets normal humanity rewards
			if (_unit getVariable ["Hero", false]) then {_player setVariable ["humanity",(_humanity - _gain),true];};
			if (_unit getVariable ["Bandit", false]) then {_player setVariable ["humanity",(_humanity + _gain),true];};
		} else {
			// Unauthorized player loses humanity instead
			if (_unit getVariable ["Hero", false]) then {_player setVariable ["humanity",(_humanity + _gain),true];};  // Lose humanity for killing heroes (double penalty)
			if (_unit getVariable ["Bandit", false]) then {_player setVariable ["humanity",(_humanity - _gain),true];};  // Lose humanity instead of gaining it for killing bandits
		};
	};

	if (WAI_KillsGain) then {
		local _banditkills = _player getVariable["banditKills",0];
		local _humankills = _player getVariable["humanKills",0];
		if (_unit getVariable ["Hero", false]) then {
			_player setVariable ["humanKills",(_humankills + 1),true];
		} else {
			_player setVariable ["banditKills",(_banditkills + 1),true];
		};
	};

	if (WAI_ClearBody) then {
		{_unit removeMagazine _x;} count (magazines _unit);
		{_unit removeWeapon _x;} count (weapons _unit);
	};

	if (WAI_ShareInfo) then {
		{
			if (((position _x) distance (position _unit)) <= WAI_ShareDist) then {
				_x reveal [_player, 4.0];
			};
		} count allUnits;
	};
} else {
	if (WAI_CleanRoadKill) then {
		removeBackpack _unit;
		removeAllWeapons _unit;
		{
			_unit removeMagazine _x
		} count magazines _unit;
	} else {
		if ((random 100) <= WAI_RkDamageWeapon) then {
			removeAllWeapons _unit;
		};
	};
};

if (WAI_RemoveLauncher && {_launcher != ""}) then {
	local _rockets = _launcher call WAI_FindAmmo;
	_unit removeWeapon _launcher;
	{
		if(_x == _rockets) then {
			_unit removeMagazine _x;
		};
	} count magazines _unit;
};

if (_unit hasWeapon "NVGoggles" && {floor(random 100) < 20}) then {
	_unit removeWeapon "NVGoggles";
};
